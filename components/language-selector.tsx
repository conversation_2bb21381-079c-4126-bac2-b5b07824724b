"use client"

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useLanguage } from "./language-provider"
import type { Language } from "@/lib/translations"

const languages = [
  { code: "en" as Language, name: "English", flag: "🇺🇸" },
  { code: "tr" as Language, name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", flag: "🇹🇷" },
  { code: "ar" as Language, name: "العربية", flag: "🇸🇦" },
  { code: "ru" as Language, name: "Русский", flag: "🇷🇺" },
]

export function LanguageSelector() {
  const { language, setLanguage } = useLanguage()

  return (
    <Select value={language} onValueChange={setLanguage}>
      <SelectTrigger className="w-32 bg-input border-border text-foreground">
        <SelectValue />
      </SelectTrigger>
      <SelectContent className="bg-popover border-border">
        {languages.map((lang) => (
          <SelectItem key={lang.code} value={lang.code}>
            <div className="flex items-center space-x-2">
              <span>{lang.flag}</span>
              <span>{lang.name}</span>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}
