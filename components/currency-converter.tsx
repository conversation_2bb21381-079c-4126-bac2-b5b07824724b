"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { ArrowUpDown } from "lucide-react"
import { useLanguage } from "./language-provider"

const currencies = [
  { code: "TRY", name: "Turkish Lira", flag: "🇹🇷" },
  { code: "USD", name: "US Dollar", flag: "🇺🇸" },
  { code: "EUR", name: "Euro", flag: "🇪🇺" },
  { code: "GBP", name: "British Pound", flag: "🇬🇧" },
  { code: "JPY", name: "Japanese Yen", flag: "🇯🇵" },
  { code: "CHF", name: "Swiss Franc", flag: "🇨🇭" },
  { code: "CAD", name: "Canadian Dollar", flag: "🇨🇦" },
]

// Mock exchange rates (TRY to other currencies)
const exchangeRates: Record<string, number> = {
  USD: 0.037,
  EUR: 0.034,
  GBP: 0.029,
  JPY: 5.42,
  CHF: 0.033,
  CAD: 0.051,
  TRY: 1,
}

export function CurrencyConverter() {
  const [fromCurrency, setFromCurrency] = useState("TRY")
  const [toCurrency, setToCurrency] = useState("USD")
  const [fromAmount, setFromAmount] = useState("100")
  const [toAmount, setToAmount] = useState("3.70")
  const { t } = useLanguage()

  const convertCurrency = (amount: string, from: string, to: string) => {
    const numAmount = Number.parseFloat(amount) || 0

    if (from === to) return numAmount.toString()

    // Convert to TRY first, then to target currency
    let tryAmount = numAmount
    if (from !== "TRY") {
      tryAmount = numAmount / exchangeRates[from]
    }

    const result = to === "TRY" ? tryAmount : tryAmount * exchangeRates[to]
    return result.toFixed(to === "JPY" ? 0 : 2)
  }

  useEffect(() => {
    const converted = convertCurrency(fromAmount, fromCurrency, toCurrency)
    setToAmount(converted)
  }, [fromAmount, fromCurrency, toCurrency])

  const handleFromAmountChange = (value: string) => {
    setFromAmount(value)
  }

  const handleToAmountChange = (value: string) => {
    setToAmount(value)
    const converted = convertCurrency(value, toCurrency, fromCurrency)
    setFromAmount(converted)
  }

  const swapCurrencies = () => {
    setFromCurrency(toCurrency)
    setToCurrency(fromCurrency)
    setFromAmount(toAmount)
  }

  return (
    <section id="converter" className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-foreground mb-2 drop-shadow-lg">{t("currencyConverter")}</h2>
        <p className="text-muted-foreground">{t("convertDescription")}</p>
      </div>

      <Card className="max-w-2xl mx-auto bg-card/80 backdrop-blur-sm border-border hover:border-primary/50 transition-all duration-300">
        <CardHeader>
          <CardTitle className="text-center text-foreground">{t("quickConvert")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* From Currency */}
          <div className="space-y-2">
            <Label htmlFor="from-amount" className="text-muted-foreground">
              {t("from")}
            </Label>
            <div className="flex space-x-2">
              <div className="flex-1">
                <Input
                  id="from-amount"
                  type="number"
                  value={fromAmount}
                  onChange={(e) => handleFromAmountChange(e.target.value)}
                  placeholder={t("enterAmount")}
                  className="text-lg bg-background/50 border-border text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-primary/50"
                />
              </div>
              <Select value={fromCurrency} onValueChange={setFromCurrency}>
                <SelectTrigger className="w-32 bg-background/50 border-border text-foreground">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-card border-border">
                  {currencies.map((currency) => (
                    <SelectItem key={currency.code} value={currency.code} className="text-foreground hover:bg-accent">
                      <div className="flex items-center space-x-2">
                        <span>{currency.flag}</span>
                        <span>{currency.code}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Swap Button */}
          <div className="flex justify-center">
            <Button
              variant="outline"
              size="sm"
              onClick={swapCurrencies}
              className="rounded-full p-2 bg-primary border-primary text-primary-foreground hover:bg-primary/80 shadow-lg shadow-primary/30"
            >
              <ArrowUpDown className="h-4 w-4" />
            </Button>
          </div>

          {/* To Currency */}
          <div className="space-y-2">
            <Label htmlFor="to-amount" className="text-muted-foreground">
              {t("to")}
            </Label>
            <div className="flex space-x-2">
              <div className="flex-1">
                <Input
                  id="to-amount"
                  type="number"
                  value={toAmount}
                  onChange={(e) => handleToAmountChange(e.target.value)}
                  placeholder={t("convertedAmount")}
                  className="text-lg bg-background/50 border-border text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-primary/50"
                />
              </div>
              <Select value={toCurrency} onValueChange={setToCurrency}>
                <SelectTrigger className="w-32 bg-background/50 border-border text-foreground">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-card border-border">
                  {currencies.map((currency) => (
                    <SelectItem key={currency.code} value={currency.code} className="text-foreground hover:bg-accent">
                      <div className="flex items-center space-x-2">
                        <span>{currency.flag}</span>
                        <span>{currency.code}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Exchange Rate Info */}
          <div className="bg-accent/50 rounded-lg p-4 text-center border border-border">
            <p className="text-sm text-muted-foreground">
              1 {fromCurrency} = {convertCurrency("1", fromCurrency, toCurrency)} {toCurrency}
            </p>
            <p className="text-xs text-muted-foreground/70 mt-1">{t("midMarketRate")}</p>
          </div>
        </CardContent>
      </Card>
    </section>
  )
}
