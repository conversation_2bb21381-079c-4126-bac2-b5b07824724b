#!/usr/bin/env node

/**
 * Test script for the currency data cron job
 * This script tests the /api/cron/update-data endpoint
 */

const https = require('https');
const http = require('http');

// Configuration
const config = {
  // Change this to your local development server or deployed URL
  baseUrl: process.env.TEST_URL || 'http://localhost:3000',
  cronSecret: process.env.CRON_SECRET || '', // Optional: add your cron secret
  timeout: 30000 // 30 seconds timeout
};

/**
 * Make HTTP request
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'CronTestScript/1.0',
        ...options.headers
      },
      timeout: config.timeout
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (options.body) {
      req.write(JSON.stringify(options.body));
    }

    req.end();
  });
}

/**
 * Test the cron endpoint
 */
async function testCronEndpoint() {
  console.log('🚀 Testing Currency Data Cron Job');
  console.log('=====================================');
  console.log(`Base URL: ${config.baseUrl}`);
  console.log(`Timeout: ${config.timeout}ms`);
  console.log('');

  try {
    // Prepare headers
    const headers = {};
    if (config.cronSecret) {
      headers['authorization'] = `Bearer ${config.cronSecret}`;
      console.log('✅ Using CRON_SECRET for authentication');
    } else {
      console.log('⚠️  No CRON_SECRET provided - endpoint may reject request');
    }

    console.log('📡 Making POST request to /api/cron/update-data...');
    const startTime = Date.now();

    const response = await makeRequest(`${config.baseUrl}/api/cron/update-data`, {
      method: 'POST',
      headers: headers,
      body: {} // Empty body for cron job
    });

    const duration = Date.now() - startTime;

    console.log('');
    console.log('📊 Response Details:');
    console.log('===================');
    console.log(`Status Code: ${response.status}`);
    console.log(`Duration: ${duration}ms`);
    console.log('');

    if (response.status === 200) {
      console.log('✅ SUCCESS - Cron job executed successfully!');
      console.log('');
      console.log('📈 Response Data:');
      console.log(JSON.stringify(response.data, null, 2));
      
      if (response.data.data) {
        console.log('');
        console.log('📋 Summary:');
        console.log(`- Total Received: ${response.data.data.totalReceived || 'N/A'}`);
        console.log(`- Successfully Stored: ${response.data.data.stored || 'N/A'}`);
        console.log(`- Errors: ${response.data.data.errors || 'N/A'}`);
        console.log(`- Execution Duration: ${response.data.data.duration || 'N/A'}`);
      }
    } else if (response.status === 401) {
      console.log('❌ UNAUTHORIZED - Check your CRON_SECRET');
      console.log('');
      console.log('💡 Tips:');
      console.log('- Set CRON_SECRET environment variable');
      console.log('- Or remove cron secret check from the endpoint for testing');
    } else {
      console.log(`❌ ERROR - HTTP ${response.status}`);
      console.log('');
      console.log('📄 Response Data:');
      console.log(JSON.stringify(response.data, null, 2));
    }

  } catch (error) {
    console.log('');
    console.log('💥 REQUEST FAILED');
    console.log('=================');
    console.log(`Error: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('');
      console.log('💡 Tips:');
      console.log('- Make sure your development server is running (npm run dev)');
      console.log('- Check if the URL is correct');
      console.log('- Verify the port number');
    }
  }
}

/**
 * Main execution
 */
async function main() {
  console.log('Currency Data Cron Job Test Script');
  console.log('==================================');
  console.log('');

  // Test cron endpoint
  await testCronEndpoint();

  console.log('');
  console.log('🏁 Test completed!');
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('Currency Data Cron Job Test Script');
  console.log('');
  console.log('Usage:');
  console.log('  node scripts/test-cron.js [options]');
  console.log('');
  console.log('Environment Variables:');
  console.log('  TEST_URL      - Base URL for testing (default: http://localhost:3000)');
  console.log('  CRON_SECRET   - Secret for cron job authentication');
  console.log('');
  console.log('Examples:');
  console.log('  node scripts/test-cron.js');
  console.log('  TEST_URL=https://your-app.vercel.app node scripts/test-cron.js');
  console.log('  CRON_SECRET=your-secret node scripts/test-cron.js');
  process.exit(0);
}

// Run the test
main().catch(console.error);
