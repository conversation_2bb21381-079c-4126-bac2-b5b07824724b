# Test Scripts for Currency Data Cron Job

This directory contains test scripts to verify the currency data fetching system.

## Available Scripts

### 1. Node.js Test Script (`test-cron.js`)

A comprehensive test script that tests both external API connectivity and the cron job endpoint.

**Usage:**
```bash
# Basic usage (tests localhost:3000)
node scripts/test-cron.js

# With custom URL
TEST_URL=https://your-app.vercel.app node scripts/test-cron.js

# With authentication
CRON_SECRET=your-secret node scripts/test-cron.js

# Using npm script
npm run test:cron
```

**Features:**
- Tests external API connectivity first
- Tests cron job endpoint with proper error handling
- Shows detailed response information
- Measures execution time
- Handles authentication with CRON_SECRET

### 2. Bash Test Script (`test-cron.sh`)

A simpler bash script for quick testing.

**Usage:**
```bash
# Basic usage
./scripts/test-cron.sh

# With custom URL
./scripts/test-cron.sh http://localhost:3000

# With URL and secret
./scripts/test-cron.sh http://localhost:3000 your-cron-secret

# With environment variable
CRON_SECRET=your-secret ./scripts/test-cron.sh

# Using npm script
npm run test:cron:bash
```

**Features:**
- Lightweight and fast
- Tests external API first
- Tests cron endpoint
- JSON formatting with jq (if available)
- Color-coded output

### 3. Quick External API Test

Test only the external API connectivity:

```bash
# Using npm script
npm run test:external-api

# Or directly with curl
curl -s http://localhost:3000/api/test-currency | python3 -m json.tool
```

## Environment Variables

- `TEST_URL` - Base URL for testing (default: `http://localhost:3000`)
- `CRON_SECRET` - Secret for cron job authentication (optional)

## Expected Responses

### Successful Cron Job Response:
```json
{
  "success": true,
  "message": "Currency data updated successfully",
  "timestamp": "2025-09-21T20:26:58.123Z",
  "task": "update-currency-data",
  "data": {
    "totalReceived": 8,
    "stored": 8,
    "errors": 0,
    "duration": "1250ms"
  }
}
```

### External API Test Response:
```json
{
  "success": true,
  "message": "Currency API connection successful",
  "timestamp": "2025-09-21T20:26:58.123Z",
  "data": {
    "totalItems": 8,
    "sampleData": [
      {
        "id": 995,
        "a": 0.2613,
        "gid": 6,
        "b": 0.2587,
        "lp": 0.26,
        "d": -1.66,
        "s": "DOGE/USD",
        "l": "2025-09-21T23:10:10.6494544"
      }
    ]
  }
}
```

## Troubleshooting

### Common Issues:

1. **Connection Refused**
   - Make sure your development server is running: `npm run dev`
   - Check if the URL and port are correct

2. **Unauthorized (401)**
   - Set the correct `CRON_SECRET` environment variable
   - Or temporarily disable authentication in the cron endpoint for testing

3. **External API Timeout**
   - Check your internet connection
   - The external API might be temporarily unavailable

4. **Database Errors**
   - Run migrations first: `POST /api/migrate` with `{"action": "full"}`
   - Check database connection settings

## Testing Workflow

1. **Start your development server:**
   ```bash
   npm run dev
   ```

2. **Run database migrations (if needed):**
   ```bash
   curl -X POST http://localhost:3000/api/migrate \
     -H "Content-Type: application/json" \
     -d '{"action": "full"}'
   ```

3. **Test the system:**
   ```bash
   npm run test:cron
   ```

4. **Check stored data:**
   ```bash
   curl -s http://localhost:3000/api/data/currencies | python3 -m json.tool
   ```
