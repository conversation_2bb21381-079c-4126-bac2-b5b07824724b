#!/bin/bash

# Test script for currency data cron job
# Usage: ./scripts/test-cron.sh [URL] [CRON_SECRET]

# Configuration
BASE_URL=${1:-"http://localhost:3000"}
CRON_SECRET=${2:-${CRON_SECRET:-""}}

echo "🚀 Testing Currency Data Cron Job"
echo "=================================="
echo "Base URL: $BASE_URL"
echo "Using CRON_SECRET: $([ -n "$CRON_SECRET" ] && echo "Yes" || echo "No")"
echo ""

# Function to make HTTP request with curl
make_request() {
    local url=$1
    local method=${2:-"GET"}
    local headers=${3:-""}
    local data=${4:-""}
    
    local curl_cmd="curl -s -w '\n%{http_code}\n' -X $method"
    
    if [ -n "$headers" ]; then
        curl_cmd="$curl_cmd -H '$headers'"
    fi
    
    if [ -n "$data" ]; then
        curl_cmd="$curl_cmd -d '$data'"
    fi
    
    curl_cmd="$curl_cmd '$url'"
    
    eval $curl_cmd
}

# Test 1: Check external API connectivity
echo "🌐 Testing External API Connectivity"
echo "===================================="
echo "📡 GET /api/test-currency"

response=$(curl -s -w '\n%{http_code}\n' "$BASE_URL/api/test-currency")
status_code=$(echo "$response" | tail -n1)
body=$(echo "$response" | head -n -1)

echo "Status Code: $status_code"

if [ "$status_code" = "200" ]; then
    echo "✅ External API connection successful!"
    echo ""
    echo "Sample response:"
    echo "$body" | head -c 500
    echo "..."
else
    echo "❌ External API test failed"
    echo "Response: $body"
fi

echo ""
echo "⏱️  Waiting 2 seconds..."
sleep 2

# Test 2: Test cron endpoint
echo "📡 Testing Cron Endpoint"
echo "========================"
echo "📡 POST /api/cron/update-data"

# Prepare headers
headers="Content-Type: application/json"
if [ -n "$CRON_SECRET" ]; then
    headers="$headers"$'\n'"Authorization: Bearer $CRON_SECRET"
    echo "🔐 Using authentication header"
fi

# Make the request
start_time=$(date +%s%3N)

if [ -n "$CRON_SECRET" ]; then
    response=$(curl -s -w '\n%{http_code}\n' \
        -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $CRON_SECRET" \
        -d '{}' \
        "$BASE_URL/api/cron/update-data")
else
    response=$(curl -s -w '\n%{http_code}\n' \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{}' \
        "$BASE_URL/api/cron/update-data")
fi

end_time=$(date +%s%3N)
duration=$((end_time - start_time))

status_code=$(echo "$response" | tail -n1)
body=$(echo "$response" | head -n -1)

echo ""
echo "📊 Response Details:"
echo "==================="
echo "Status Code: $status_code"
echo "Duration: ${duration}ms"
echo ""

case $status_code in
    200)
        echo "✅ SUCCESS - Cron job executed successfully!"
        echo ""
        echo "📈 Response Data:"
        echo "$body" | python3 -m json.tool 2>/dev/null || echo "$body"
        
        # Extract summary info if possible
        if command -v jq >/dev/null 2>&1; then
            echo ""
            echo "📋 Summary:"
            echo "- Total Received: $(echo "$body" | jq -r '.data.totalReceived // "N/A"')"
            echo "- Successfully Stored: $(echo "$body" | jq -r '.data.stored // "N/A"')"
            echo "- Errors: $(echo "$body" | jq -r '.data.errors // "N/A"')"
            echo "- Execution Duration: $(echo "$body" | jq -r '.data.duration // "N/A"')"
        fi
        ;;
    401)
        echo "❌ UNAUTHORIZED - Check your CRON_SECRET"
        echo ""
        echo "💡 Tips:"
        echo "- Set CRON_SECRET environment variable"
        echo "- Or pass it as second argument: ./test-cron.sh URL SECRET"
        echo "- Or remove cron secret check from endpoint for testing"
        ;;
    *)
        echo "❌ ERROR - HTTP $status_code"
        echo ""
        echo "📄 Response Data:"
        echo "$body"
        ;;
esac

echo ""
echo "🏁 Test completed!"
echo ""
echo "💡 Usage Examples:"
echo "  ./scripts/test-cron.sh"
echo "  ./scripts/test-cron.sh http://localhost:3000"
echo "  ./scripts/test-cron.sh http://localhost:3000 your-cron-secret"
echo "  CRON_SECRET=your-secret ./scripts/test-cron.sh"
