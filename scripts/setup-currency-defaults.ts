import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function setupCurrencyDefaults() {
  console.log('Setting up currency defaults...');

  try {
    // Flag mappings
    const flagUpdates = [
      { pattern: 'USD', flag: '🇺🇸' },
      { pattern: 'EUR', flag: '🇪🇺' },
      { pattern: 'GBP', flag: '🇬🇧' },
      { pattern: 'JPY', flag: '🇯🇵' },
      { pattern: 'CHF', flag: '🇨🇭' },
      { pattern: 'CAD', flag: '🇨🇦' },
      { pattern: 'AUD', flag: '🇦🇺' },
      { pattern: 'SEK', flag: '🇸🇪' },
      { pattern: 'NOK', flag: '🇳🇴' },
      { pattern: 'DKK', flag: '🇩🇰' },
      { pattern: 'BTC', flag: '₿' },
      { pattern: 'ETH', flag: '⟠' },
      { pattern: 'GOLD', flag: '🥇' },
      { pattern: 'XAU', flag: '🥇' },
      { pattern: 'SILVER', flag: '🥈' },
      { pattern: 'XAG', flag: '🥈' },
      { pattern: 'BIST', flag: '📈' },
    ];

    // Update flags
    for (const { pattern, flag } of flagUpdates) {
      const result = await prisma.currency.updateMany({
        where: {
          symbol: {
            contains: pattern,
            mode: 'insensitive',
          },
          flag: null,
        },
        data: {
          flag: flag,
        },
      });
      console.log(`Updated ${result.count} currencies with pattern "${pattern}" to flag "${flag}"`);
    }

    // Set popular currencies as favorites
    const favoritePatterns = [
      'USD/TRY',
      'EUR/TRY', 
      'GBP/TRY',
      'BTC/USD',
      'ALTIN KG/TRY',
      'GÜMÜŞ KG/TRY'
    ];

    for (const pattern of favoritePatterns) {
      const result = await prisma.currency.updateMany({
        where: {
          OR: [
            { symbol: { equals: pattern, mode: 'insensitive' } },
            { symbol: { contains: pattern, mode: 'insensitive' } },
          ],
        },
        data: {
          isFavorite: true,
        },
      });
      console.log(`Set ${result.count} currencies matching "${pattern}" as favorites`);
    }

    console.log('Currency defaults setup completed!');
  } catch (error) {
    console.error('Error setting up currency defaults:', error);
  } finally {
    await prisma.$disconnect();
  }
}

setupCurrencyDefaults();
