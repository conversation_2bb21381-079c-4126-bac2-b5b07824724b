import { NextRequest, NextResponse } from 'next/server';
import { getCurrencies, fetchCurrencyData, storeCurrencyData } from '@/lib/currency-service';

// GET - Fetch currency data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const symbol = searchParams.get('symbol');
    const groupId = searchParams.get('groupId') ? parseInt(searchParams.get('groupId')!) : undefined;
    
    const currencies = await getCurrencies({
      limit,
      offset,
      symbol: symbol || undefined,
      groupId
    });
    
    return NextResponse.json({
      success: true,
      data: currencies,
      count: currencies.length,
      limit,
      offset
    });
    
  } catch (error) {
    console.error('Error fetching currencies:', error);
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

// POST - Manually fetch and update currency data from external API
export async function POST(request: NextRequest) {
  try {
    console.log('Manual currency data update triggered...');
    
    // Fetch data from external API
    const currencyData = await fetchCurrencyData();
    
    if (!currencyData || currencyData.length === 0) {
      return NextResponse.json(
        { 
          success: false,
          error: 'No currency data received from external API' 
        },
        { status: 400 }
      );
    }
    
    // Store the data in database
    const result = await storeCurrencyData(currencyData);
    
    return NextResponse.json({
      success: true,
      message: 'Currency data updated successfully',
      data: {
        totalReceived: currencyData.length,
        stored: result.stored,
        errors: result.errors
      }
    });
    
  } catch (error) {
    console.error('Error updating currency data:', error);
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
