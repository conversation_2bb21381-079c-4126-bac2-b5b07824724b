import { NextRequest, NextResponse } from 'next/server';
import { getCurrencyHistory } from '@/lib/currency-service';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// GET - Fetch currency price history
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const currencyId = searchParams.get('currencyId');
    const limit = parseInt(searchParams.get('limit') || '100');
    const offset = parseInt(searchParams.get('offset') || '0');
    const fromDate = searchParams.get('fromDate') ? new Date(searchParams.get('fromDate')!) : undefined;
    const toDate = searchParams.get('toDate') ? new Date(searchParams.get('toDate')!) : undefined;
    
    if (!currencyId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'currencyId parameter is required' 
        },
        { status: 400 }
      );
    }
    
    const history = await getCurrencyHistory(parseInt(currencyId), {
      limit,
      offset,
      fromDate,
      toDate
    });
    
    return NextResponse.json({
      success: true,
      data: history,
      count: history.length,
      limit,
      offset,
      currencyId: parseInt(currencyId)
    });
    
  } catch (error) {
    console.error('Error fetching currency history:', error);
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
