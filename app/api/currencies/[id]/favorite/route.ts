import { NextRequest, NextResponse } from 'next/server';
import { updateCurrencyFavorite } from '@/lib/currency-service';

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // try {
  //   const currencyId = parseInt(params.id);
  //   const { isFavorite } = await request.json();

  //   if (isNaN(currencyId)) {
  //     return NextResponse.json(
  //       { success: false, error: 'Invalid currency ID' },
  //       { status: 400 }
  //     );
  //   }

  //   if (typeof isFavorite !== 'boolean') {
  //     return NextResponse.json(
  //       { success: false, error: 'isFavorite must be a boolean' },
  //       { status: 400 }
  //     );
  //   }

  //   const updatedCurrency = await updateCurrencyFavorite(currencyId, isFavorite);

  //   return NextResponse.json({
  //     success: true,
  //     data: {
  //       id: updatedCurrency.id,
  //       symbol: updatedCurrency.symbol,
  //       isFavorite: updatedCurrency.isFavorite,
  //     },
  //   });

  // } catch (error) {
  //   console.error('Error updating currency favorite status:', error);
  //   return NextResponse.json(
  //     { 
  //       success: false, 
  //       error: 'Failed to update favorite status' 
  //     },
  //     { status: 500 }
  //   );
  // }
}
