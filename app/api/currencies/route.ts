import { NextRequest, NextResponse } from 'next/server';
import { getCurrencies, getFavoriteCurrencies, searchCurrencies } from '@/lib/currency-service';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const favoritesOnly = searchParams.get('favorites') === 'true';
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    let currencies;

    if (query) {
      // Search currencies
      currencies = await searchCurrencies(query, limit);
    } else if (favoritesOnly) {
      // Get favorite currencies
      currencies = await getFavoriteCurrencies(limit);
    } else {
      // Get all currencies with optional filters
      currencies = await getCurrencies({
        limit,
        offset,
        favoritesOnly: false,
      });
    }

    // Transform the data for frontend consumption
    const transformedCurrencies = currencies.map((currency) => ({
      id: currency.id,
      symbol: currency.symbol,
      rate: parseFloat(currency.lastPrice.toString()),
      change: currency.dailyChange ? parseFloat(currency.dailyChange.toString()) : 0,
      flag: currency.flag || getDefaultFlag(currency.symbol),
      isFavorite: currency.isFavorite,
      askPrice: currency.askPrice ? parseFloat(currency.askPrice.toString()) : null,
      bidPrice: currency.bidPrice ? parseFloat(currency.bidPrice.toString()) : null,
      lastUpdated: currency.lastUpdated,
    }));

    return NextResponse.json({
      success: true,
      data: transformedCurrencies,
      total: transformedCurrencies.length,
    });

  } catch (error) {
    console.error('Error fetching currencies:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch currencies',
        data: []
      },
      { status: 500 }
    );
  }
}

// Helper function to get default flags based on currency symbol
function getDefaultFlag(symbol: string): string {
  const flagMap: { [key: string]: string } = {
    'USD/TRY': '🇺🇸',
    'EUR/TRY': '🇪🇺',
    'GBP/TRY': '🇬🇧',
    'JPY/TRY': '🇯🇵',
    'CHF/TRY': '🇨🇭',
    'CAD/TRY': '🇨🇦',
    'AUD/TRY': '🇦🇺',
    'SEK/TRY': '🇸🇪',
    'NOK/TRY': '🇳🇴',
    'DKK/TRY': '🇩🇰',
    'BTC/USD': '₿',
    'ETH/USD': '⟠',
    'GOLD': '🥇',
    'SILVER': '🥈',
    'BIST100': '📈',
    'BIST30': '📊',
  };

  // Try exact match first
  if (flagMap[symbol]) {
    return flagMap[symbol];
  }

  // Try partial matches
  for (const [key, flag] of Object.entries(flagMap)) {
    if (symbol.includes(key.split('/')[0])) {
      return flag;
    }
  }

  // Default flag
  return '💱';
}
