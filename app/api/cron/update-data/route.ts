import { NextRequest, NextResponse } from 'next/server';
import { updateCurrencyData } from '@/lib/currency-service';

// This function will be called by Vercel cron jobs
export async function POST(request: NextRequest) {
  try {
    // Verify the request is from Vercel cron (optional security measure)
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;

    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('Starting scheduled currency data update via API route...');

    // Use the common currency update function
    const result = await updateCurrencyData();

    if (result.success) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json(result, { status: 500 });
    }

  } catch (error) {
    console.error('API route error:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        task: 'update-currency-data',
        duration: '0ms'
      },
      { status: 500 }
    );
  }
}

// Also support GET for manual testing
export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Cron endpoint is active. Use POST to trigger the job.',
    endpoint: '/api/cron/update-data',
    method: 'POST'
  });
}
