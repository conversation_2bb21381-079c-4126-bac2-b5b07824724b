import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { fetchCurrencyData, storeCurrencyData, storeCurrencyDataChunked } from '@/lib/currency-service';

// This function will be called by Vercel cron jobs
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // Verify the request is from Vercel cron (optional security measure)
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;
    
    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('Starting scheduled currency data update...');
    
    const timestamp = new Date().toISOString();
    
    // Fetch currency data from external API
    const currencyData = await fetchCurrencyData();
    
    if (!currencyData || currencyData.length === 0) {
      throw new Error('No currency data received from external API');
    }
    
    // Store the data in database - use chunked approach for large datasets
    console.log(`Received ${currencyData.length} currencies from API`);
    
    let result;
    if (currencyData.length > 300) {
      // Use chunked approach for large datasets to avoid timeouts
      console.log('Using chunked storage for large dataset...');
      result = await storeCurrencyDataChunked(currencyData, 100);
    } else {
      // Use regular bulk storage for smaller datasets
      console.log('Using bulk storage for dataset...');
      result = await storeCurrencyData(currencyData);
    }
    
    const duration = result.duration || (Date.now() - startTime);
    
    // Log successful execution
    await prisma.cronLog.create({
      data: {
        taskName: 'update-currency-data',
        executedAt: new Date(timestamp),
        status: 'success',
        durationMs: duration,
      },
    });
    
    const logMessage = currencyData.length > 300 
      ? `Currency data update completed successfully using chunked storage. Stored: ${result.stored}, Errors: ${result.errors}, Chunks: ${'chunks' in result ? result.chunks : 'N/A'}, Duration: ${duration}ms`
      : `Currency data update completed successfully using bulk storage. Stored: ${result.stored}, Errors: ${result.errors}, Duration: ${duration}ms`;
    
    console.log(logMessage);
    
    return NextResponse.json({
      success: true,
      message: 'Currency data updated successfully',
      timestamp,
      task: 'update-currency-data',
      data: {
        totalReceived: currencyData.length,
        stored: result.stored,
        errors: result.errors,
        duration: `${duration}ms`,
        method: currencyData.length > 300 ? 'chunked' : 'bulk',
        chunks: 'chunks' in result ? result.chunks : undefined
      }
    });
    
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error('Cron job error:', error);
    
    // Log the error to database if possible
    try {
      await prisma.cronLog.create({
        data: {
          taskName: 'update-currency-data',
          executedAt: new Date(),
          status: 'error',
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
          durationMs: duration,
        },
      });
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        task: 'update-currency-data',
        duration: `${duration}ms`
      },
      { status: 500 }
    );
  }
}

// Also support GET for manual testing
export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Cron endpoint is active. Use POST to trigger the job.',
    endpoint: '/api/cron/update-data',
    method: 'POST'
  });
}
