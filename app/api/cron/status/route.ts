import { NextRequest, NextResponse } from 'next/server';
import { getCronJobStatus } from '@/lib/cron-service';

// GET - Check cron job status
export async function GET(request: NextRequest) {
  try {
    // Verify the request is authorized (same auth as update-data route)
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;

    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const status = getCronJobStatus();

    return NextResponse.json({
      success: true,
      status: {
        cronJobRunning: status.running,
        cronExpression: status.expression,
        taskCurrentlyRunning: status.taskRunning,
        message: status.running
          ? (status.taskRunning ? 'Cron job is active and a task is currently running' : 'Cron job is active and ready')
          : 'Cron job is not running'
      }
    });
  } catch (error) {
    console.error('Error getting cron status:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
