@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Updated to futuristic cyan-based color scheme */
  --background: #f8fafc; /* Light slate background */
  --foreground: #1e293b; /* Dark slate text */
  --card: rgba(255, 255, 255, 0.8); /* Semi-transparent white cards */
  --card-foreground: #1e293b;
  --popover: rgba(255, 255, 255, 0.95); /* Semi-transparent popover */
  --popover-foreground: #1e293b;
  --primary: #0891b2; /* Cyan-600 primary */
  --primary-foreground: #ffffff;
  --secondary: #10b981; /* Emerald-600 secondary */
  --secondary-foreground: #ffffff;
  --muted: #f1f5f9; /* Light slate for muted elements */
  --muted-foreground: #64748b;
  --accent: #10b981; /* Emerald accent */
  --accent-foreground: #ffffff;
  --destructive: #ef4444; /* Red for destructive actions */
  --destructive-foreground: #ffffff;
  --border: #cbd5e1;
  --input: rgba(255, 255, 255, 0.8);
  --ring: rgba(8, 145, 178, 0.3); /* Cyan ring */
  --chart-1: #0891b2;
  --chart-2: #10b981;
  --chart-3: #164e63;
  --chart-4: #4b5563;
  --chart-5: #ffffff;
  --radius: 0.5rem;
  --sidebar: #ffffff;
  --sidebar-foreground: #4b5563;
  --sidebar-primary: #0891b2;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #10b981;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #4b5563;
  --sidebar-ring: rgba(8, 145, 178, 0.5);
}

.dark {
  /* Updated dark mode with futuristic colors */
  --background: #0f172a; /* Dark slate background */
  --foreground: #e2e8f0; /* Light slate text */
  --card: #1e293b; /* Dark slate cards */
  --card-foreground: #e2e8f0;
  --popover: rgba(30, 41, 59, 0.95);
  --popover-foreground: #e2e8f0;
  --primary: #06b6d4; /* Cyan-500 for dark mode */
  --primary-foreground: #0f172a;
  --secondary: #10b981;
  --secondary-foreground: #0f172a;
  --muted: #334155;
  --muted-foreground: #94a3b8;
  --accent: #10b981;
  --accent-foreground: #0f172a;
  --destructive: #ff4757;
  --destructive-foreground: #ffffff;
  --border: #334155;
  --input: #1e293b;
  --ring: rgba(6, 182, 212, 0.5);
  --chart-1: #06b6d4;
  --chart-2: #10b981;
  --chart-3: #164e63;
  --chart-4: #94a3b8;
  --chart-5: #e2e8f0;
  --radius: 0.5rem;
  --sidebar: #1e293b;
  --sidebar-foreground: #e2e8f0;
  --sidebar-primary: #06b6d4;
  --sidebar-primary-foreground: #0f172a;
  --sidebar-accent: #334155;
  --sidebar-accent-foreground: #e2e8f0;
  --sidebar-border: #334155;
  --sidebar-ring: rgba(6, 182, 212, 0.5);
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Added futuristic gradient backgrounds */
  .futuristic-bg {
    background: linear-gradient(135deg, #0891b2 0%, #10b981 100%);
  }

  .futuristic-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(8, 145, 178, 0.2);
  }

  .dark .futuristic-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glow-effect {
    box-shadow: 0 0 20px rgba(8, 145, 178, 0.2);
  }

  .dark .glow-effect {
    box-shadow: 0 0 20px rgba(8, 145, 178, 0.3);
  }
}
