import { CurrencyHeader } from "@/components/currency-header"
import { ExchangeRateDisplay } from "@/components/exchange-rate-display"
import { CurrencyConverter } from "@/components/currency-converter"
import { CurrencyFooter } from "@/components/currency-footer"

export default function CurrencyPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-primary/10 to-background dark:from-slate-900 dark:via-cyan-900 dark:to-slate-900">
      <CurrencyHeader />
      <main className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="space-y-8">
          <ExchangeRateDisplay />
          <CurrencyConverter />
        </div>
      </main>
      <CurrencyFooter />
    </div>
  )
}
