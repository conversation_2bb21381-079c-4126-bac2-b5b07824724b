# Prisma Migration Guide

This document outlines the migration from Vercel Postgres client to Prisma ORM.

## What Changed

### Dependencies
- **Removed**: `@vercel/postgres`, `pg`, `@types/pg`
- **Added**: `prisma`, `@prisma/client`

### Database Client
- **Old**: Custom `query()` function from `lib/db.ts`
- **New**: Prisma Client from `lib/prisma.ts`

### Schema Management
- **Old**: Raw SQL in `lib/schema.sql`
- **New**: Prisma schema in `prisma/schema.prisma` (with SQL schema still used for migrations)

## Environment Variables

### Required
```env
DATABASE_URL="postgresql://username:password@host:port/database"
```

### For Vercel Deployment
Vercel will automatically set `DATABASE_URL` when you connect a Postgres database.

## Key Files Updated

1. **lib/prisma.ts** - New Prisma client configuration
2. **prisma/schema.prisma** - Prisma schema definition
3. **lib/currency-service.ts** - Updated to use Prisma client
4. **app/api/cron/update-data/route.ts** - Updated to use Prisma client
5. **app/api/health/route.ts** - Updated to use Prisma client
6. **lib/migrate.ts** - Updated to use Prisma client
7. **package.json** - Added Prisma scripts

## New Scripts

```bash
# Generate Prisma client
pnpm db:generate

# Push schema to database (for development)
pnpm db:push

# Create and run migrations
pnpm db:migrate

# Open Prisma Studio (database GUI)
pnpm db:studio

# Seed database
pnpm db:seed
```

## Migration Steps

1. **Set up environment variables**:
   ```env
   DATABASE_URL="postgresql://username:password@host:port/database"
   ```

2. **Generate Prisma client**:
   ```bash
   pnpm db:generate
   ```

3. **Push schema to database** (for existing databases):
   ```bash
   pnpm db:push
   ```

4. **Test the application**:
   ```bash
   pnpm dev
   ```

## Benefits of Prisma

1. **Type Safety**: Full TypeScript support with auto-generated types
2. **Better Developer Experience**: IntelliSense, auto-completion
3. **Query Builder**: Intuitive API for database operations
4. **Migration Management**: Built-in migration system
5. **Database Introspection**: Generate schema from existing database
6. **Prisma Studio**: Visual database browser

## API Changes

### Before (Raw SQL)
```typescript
const result = await query('SELECT * FROM currencies WHERE symbol = $1', [symbol]);
return result.rows;
```

### After (Prisma)
```typescript
const currencies = await prisma.currency.findMany({
  where: { symbol: { contains: symbol, mode: 'insensitive' } }
});
return currencies;
```

## Troubleshooting

### Common Issues

1. **Environment Variables**: Ensure `DATABASE_URL` is properly set
2. **Client Generation**: Run `pnpm db:generate` after schema changes
3. **Database Connection**: Check database credentials and connectivity

### Rollback Plan

If issues occur, the old `lib/db.ts` file is backed up as `lib/db.ts.backup` and can be restored if needed.
