import { prisma } from './prisma';

// Interface for the API response structure
export interface CurrencyData {
  id: number;
  a: number | null; // ask price
  gid: number; // group id
  b: number | null; // bid price
  lp: number; // last price
  d: number | null; // daily change
  ba: number | null; // bank ask
  o: number | null; // open price
  p: number; // precision
  s: string; // symbol
  l: string; // last updated timestamp
}

// Function to fetch currency data from external API
export async function fetchCurrencyData(): Promise<CurrencyData[]> {
  try {
    const response = await fetch('https://tstapi.iarplatform.com/api/ForInvest/GetAllUpdateData?secound=60', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'PiyasalarInfo/1.0'
      },
      // Add timeout
      signal: AbortSignal.timeout(30000) // 30 second timeout
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: CurrencyData[] = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching currency data:', error);
    throw error;
  }
}

// Function to store currency data in database using bulk operations
export async function storeCurrencyData(currencies: CurrencyData[]): Promise<{ stored: number; errors: number; duration: number }> {
  if (currencies.length === 0) {
    return { stored: 0, errors: 0, duration: 0 };
  }

  const startTime = Date.now();
  let stored = 0;
  let errors = 0;

  try {
    console.log(`Starting bulk storage of ${currencies.length} currencies...`);
    
    // Use a transaction to ensure data consistency
    await prisma.$transaction(async (tx) => {
      // Prepare currency data for bulk operations
      const currencyDataForUpsert = currencies.map(currency => {
        const lastUpdated = new Date(currency.l);
        return {
          id: currency.id,
          symbol: currency.s,
          askPrice: currency.a,
          bidPrice: currency.b,
          lastPrice: currency.lp,
          dailyChange: currency.d,
          groupId: currency.gid,
          precisionDigits: currency.p,
          openPrice: currency.o,
          bankAsk: currency.ba,
          lastUpdated: lastUpdated,
        };
      });

      // Prepare history data for bulk insert
      const historyDataForInsert = currencies.map(currency => {
        const lastUpdated = new Date(currency.l);
        return {
          currencyId: currency.id,
          symbol: currency.s,
          askPrice: currency.a,
          bidPrice: currency.b,
          lastPrice: currency.lp,
          dailyChange: currency.d,
          groupId: currency.gid,
          precisionDigits: currency.p,
          openPrice: currency.o,
          bankAsk: currency.ba,
          lastUpdated: lastUpdated,
          source: 'iarplatform_api',
        };
      });

      // Get existing currency IDs to determine which ones to update vs create
      const existingCurrencies = await tx.currency.findMany({
        where: {
          id: {
            in: currencies.map(c => c.id)
          }
        },
        select: { id: true }
      });

      const existingIds = new Set(existingCurrencies.map(c => c.id));
      
      // Separate data into updates and creates
      const currenciesToUpdate = currencyDataForUpsert.filter(c => existingIds.has(c.id));
      const currenciesToCreate = currencyDataForUpsert.filter(c => !existingIds.has(c.id));

      // Bulk create new currencies
      if (currenciesToCreate.length > 0) {
        await tx.currency.createMany({
          data: currenciesToCreate,
          skipDuplicates: true
        });
        stored += currenciesToCreate.length;
      }

      // Bulk update existing currencies
      if (currenciesToUpdate.length > 0) {
        // For better performance with large datasets, we'll batch the updates
        const BATCH_SIZE = 50;
        for (let i = 0; i < currenciesToUpdate.length; i += BATCH_SIZE) {
          const batch = currenciesToUpdate.slice(i, i + BATCH_SIZE);
          
          // Use Promise.all for concurrent updates within each batch
          await Promise.all(
            batch.map(currency =>
              tx.currency.update({
                where: { id: currency.id },
                data: {
                  symbol: currency.symbol,
                  askPrice: currency.askPrice,
                  bidPrice: currency.bidPrice,
                  lastPrice: currency.lastPrice,
                  dailyChange: currency.dailyChange,
                  groupId: currency.groupId,
                  precisionDigits: currency.precisionDigits,
                  openPrice: currency.openPrice,
                  bankAsk: currency.bankAsk,
                  lastUpdated: currency.lastUpdated,
                }
              })
            )
          );
        }
        stored += currenciesToUpdate.length;
      }

      // Bulk insert history records
      if (historyDataForInsert.length > 0) {
        await tx.currencyHistory.createMany({
          data: historyDataForInsert,
          skipDuplicates: false // We want all history records
        });
      }
    }, {
      timeout: 60000, // 60 second timeout for large datasets
    });

    const duration = Date.now() - startTime;
    console.log(`Successfully stored ${stored} currencies in bulk operation (${duration}ms)`);
    return { stored, errors, duration };
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error('Error in bulk currency storage:', error);
    errors = currencies.length;
    stored = 0;
    return { stored, errors, duration };
  }
}

// Alternative function for extremely large datasets (1000+ currencies)
// Processes data in chunks to avoid memory issues and transaction timeouts
export async function storeCurrencyDataChunked(
  currencies: CurrencyData[], 
  chunkSize: number = 100
): Promise<{ stored: number; errors: number; duration: number; chunks: number }> {
  if (currencies.length === 0) {
    return { stored: 0, errors: 0, duration: 0, chunks: 0 };
  }

  const startTime = Date.now();
  let totalStored = 0;
  let totalErrors = 0;
  const chunks = Math.ceil(currencies.length / chunkSize);

  console.log(`Processing ${currencies.length} currencies in ${chunks} chunks of ${chunkSize}...`);

  for (let i = 0; i < currencies.length; i += chunkSize) {
    const chunk = currencies.slice(i, i + chunkSize);
    const chunkIndex = Math.floor(i / chunkSize) + 1;
    
    try {
      console.log(`Processing chunk ${chunkIndex}/${chunks} (${chunk.length} currencies)...`);
      const result = await storeCurrencyData(chunk);
      totalStored += result.stored;
      totalErrors += result.errors;
    } catch (error) {
      console.error(`Error processing chunk ${chunkIndex}:`, error);
      totalErrors += chunk.length;
    }
  }

  const duration = Date.now() - startTime;
  console.log(`Completed chunked processing: ${totalStored} stored, ${totalErrors} errors in ${duration}ms`);
  
  return { 
    stored: totalStored, 
    errors: totalErrors, 
    duration, 
    chunks 
  };
}

// Function to get currency data from database
export async function getCurrencies(options: {
  limit?: number;
  offset?: number;
  symbol?: string;
  groupId?: number;
  favoritesOnly?: boolean;
} = {}) {
  const { limit = 50, offset = 0, symbol, groupId, favoritesOnly = false } = options;
  
  const where: any = {};

  if (symbol) {
    where.symbol = {
      contains: symbol,
      mode: 'insensitive',
    };
  }

  if (groupId !== undefined) {
    where.groupId = groupId;
  }

  if (favoritesOnly) {
    where.isFavorite = true;
  }

  const currencies = await prisma.currency.findMany({
    where,
    orderBy: [
      { isFavorite: 'desc' }, // Show favorites first
      { updatedAt: 'desc' },
    ],
    take: limit,
    skip: offset,
  });

  return currencies;
}

// Function to get favorite currencies for home page
export async function getFavoriteCurrencies(limit: number = 10) {
  const currencies = await prisma.currency.findMany({
    where: {
      isFavorite: true,
    },
    orderBy: {
      updatedAt: 'desc',
    },
    take: limit,
  });

  return currencies;
}

// Function to search currencies
export async function searchCurrencies(query: string, limit: number = 20) {
  const currencies = await prisma.currency.findMany({
    where: {
      symbol: {
        contains: query,
        mode: 'insensitive',
      },
    },
    orderBy: [
      { isFavorite: 'desc' }, // Show favorites first in search results
      { updatedAt: 'desc' },
    ],
    take: limit,
  });

  return currencies;
}

// Function to update currency favorite status
export async function updateCurrencyFavorite(currencyId: number, isFavorite: boolean) {
  const currency = await prisma.currency.update({
    where: { id: currencyId },
    data: { isFavorite },
  });

  return currency;
}

// Function to get currency history
export async function getCurrencyHistory(currencyId: number, options: {
  limit?: number;
  offset?: number;
  fromDate?: Date;
  toDate?: Date;
} = {}) {
  const { limit = 100, offset = 0, fromDate, toDate } = options;
  
  const where: any = {
    currencyId: currencyId,
  };

  if (fromDate || toDate) {
    where.recordedAt = {};
    if (fromDate) {
      where.recordedAt.gte = fromDate;
    }
    if (toDate) {
      where.recordedAt.lte = toDate;
    }
  }

  const history = await prisma.currencyHistory.findMany({
    where,
    orderBy: {
      recordedAt: 'desc',
    },
    take: limit,
    skip: offset,
  });

  return history;
}
