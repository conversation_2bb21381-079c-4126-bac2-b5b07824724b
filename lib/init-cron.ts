import { startCurrencyUpdateCron } from './cron-service';

// Initialize cron jobs when the application starts
// This will only run on the server side
if (typeof window === 'undefined') {
  // Only start cron jobs in production or when explicitly enabled
  const shouldStartCron = process.env.NODE_ENV === 'production' || process.env.ENABLE_CRON === 'true';
  
  if (shouldStartCron) {
    console.log('Initializing cron jobs...');
    startCurrencyUpdateCron();
  } else {
    console.log('Cron jobs disabled in development mode. Set ENABLE_CRON=true to enable.');
  }
}

export {};
