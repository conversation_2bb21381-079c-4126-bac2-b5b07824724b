import * as cron from 'node-cron';
import { updateCurrencyData } from './currency-service';

let cronJob: cron.ScheduledTask | null = null;
let isTaskRunning = false;

// Initialize and start the cron job
export function startCurrencyUpdateCron(): void {
  // Get cron expression from environment variable, default to every 60 seconds
  const cronExpression = process.env.CRON_EXPRESSION || '*/60 * * * * *';
  
  console.log(`Initializing currency update cron job with expression: ${cronExpression}`);
  
  // Validate cron expression
  if (!cron.validate(cronExpression)) {
    console.error(`Invalid cron expression: ${cronExpression}`);
    return;
  }
  
  // Stop existing cron job if running
  if (cronJob) {
    cronJob.stop();
    cronJob = null;
  }
  
  // Create and start new cron job
  cronJob = cron.schedule(cronExpression, async () => {
    // Check if a task is already running
    if (isTaskRunning) {
      console.log('Cron job skipped: Previous task is still running');
      return;
    }

    console.log('Cron job triggered: Starting currency data update...');
    isTaskRunning = true;

    try {
      const result = await updateCurrencyData();

      if (result.success) {
        console.log(`Cron job completed successfully: ${result.message}`);
        console.log(`Data: ${JSON.stringify(result.data)}`);
      } else {
        console.error(`Cron job failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Cron job execution error:', error);
    } finally {
      // Always reset the flag, even if an error occurred
      isTaskRunning = false;
      console.log('Cron job task completed, ready for next execution');
    }
  }, {
    timezone: 'UTC'
  });
  
  console.log('Currency update cron job started successfully');
}

// Stop the cron job
export function stopCurrencyUpdateCron(): void {
  if (cronJob) {
    cronJob.stop();
    cronJob = null;
    isTaskRunning = false; // Reset the flag when stopping
    console.log('Currency update cron job stopped');
  }
}

// Get cron job status
export function getCronJobStatus(): {
  running: boolean;
  expression?: string;
  taskRunning: boolean;
} {
  return {
    running: cronJob !== null && cronJob.getStatus() === 'scheduled',
    expression: process.env.CRON_EXPRESSION || '*/60 * * * * *',
    taskRunning: isTaskRunning
  };
}

// Restart the cron job (useful for updating configuration)
export function restartCurrencyUpdateCron(): void {
  console.log('Restarting currency update cron job...');
  stopCurrencyUpdateCron();
  startCurrencyUpdateCron();
}
