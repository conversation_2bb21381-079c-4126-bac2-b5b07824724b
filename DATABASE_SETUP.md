# PostgreSQL Setup for Piyasalar Info

This guide explains how to set up PostgreSQL for your Next.js application with Vercel cron jobs.

## 🚀 Quick Start

### 1. Local Development Setup

1. **Install PostgreSQL locally** (if not already installed):
   ```bash
   # macOS with Homebrew
   brew install postgresql
   brew services start postgresql
   
   # Create database
   createdb piyasalar_info
   ```

2. **Set up environment variables**:
   ```bash
   cp env.example .env.local
   ```
   
   Edit `.env.local` with your local database credentials:
   ```env
   DATABASE_URL=postgresql://postgres:your_password_here@localhost:5432/piyasalar_info
   CRON_SECRET=your_secret_key_here
   ```

3. **Run database migrations**:
   ```bash
   # Start your Next.js development server
   npm run dev
   
   # Then call the migration API
   curl -X POST http://localhost:3000/api/migrate \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your_secret_key_here" \
     -d '{"action": "full"}'
   ```

### 2. Vercel Deployment Setup

1. **Add Vercel Postgres Integration**:
   - Go to your Vercel dashboard
   - Select your project
   - Go to "Storage" tab
   - Add "Postgres" integration
   - This automatically sets `POSTGRES_URL` environment variable

2. **Set environment variables in Vercel**:
   - `CRON_SECRET`: A secure random string for cron job authentication
   - `MIGRATION_SECRET`: (Optional) Same as CRON_SECRET or different for migration endpoint

3. **Deploy and run initial migration**:
   ```bash
   # After deployment, run migration
   curl -X POST https://your-app.vercel.app/api/migrate \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your_cron_secret" \
     -d '{"action": "full"}'
   ```

## 📊 Database Schema

The application includes these tables:

- **`currencies`**: Stores currency exchange rates
- **`cron_logs`**: Tracks cron job executions
- **`currency_history`**: Historical currency rate data

## 🔄 Cron Jobs

### Configured Cron Jobs

- **Data Update**: Runs every 6 hours (`0 */6 * * *`)
  - Endpoint: `/api/cron/update-data`
  - Purpose: Update currency rates from external APIs

### Cron Schedule Examples

```json
{
  "crons": [
    {
      "path": "/api/cron/update-data",
      "schedule": "0 */6 * * *"  // Every 6 hours
    }
  ]
}
```

Common cron patterns:
- `0 0 * * *` - Daily at midnight
- `0 */4 * * *` - Every 4 hours
- `0 9 * * 1` - Every Monday at 9 AM
- `*/30 * * * *` - Every 30 minutes

## 🛠 API Endpoints

### Health Check
```bash
GET /api/health
```
Returns database connection status.

### Currency Data
```bash
# Get currencies
GET /api/data/currencies?limit=10&offset=0&currency=EUR

# Add/update currency
POST /api/data/currencies
{
  "currency_code": "EUR",
  "rate": 0.85,
  "base_currency": "USD"
}
```

### Database Migration
```bash
# Check migration status
GET /api/migrate

# Run migrations
POST /api/migrate
{
  "action": "migrate"  // or "seed", "full", "check"
}
```

### Cron Jobs
```bash
# Manual trigger (for testing)
POST /api/cron/update-data
Authorization: Bearer your_cron_secret
```

## 🔧 Development Commands

```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Check database health
curl http://localhost:3000/api/health

# Run migrations locally
curl -X POST http://localhost:3000/api/migrate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_secret" \
  -d '{"action": "full"}'

# Test cron job locally
curl -X POST http://localhost:3000/api/cron/update-data \
  -H "Authorization: Bearer your_secret"
```

## 🔒 Security

- Cron endpoints are protected with `CRON_SECRET`
- Migration endpoints use the same secret or `MIGRATION_SECRET`
- Database connections use SSL in production
- Environment variables are properly configured for each environment

## 📝 Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `POSTGRES_URL` | Full database connection string (Vercel) | Production |
| `POSTGRES_USER` | Database username (Local) | Development |
| `POSTGRES_HOST` | Database host (Local) | Development |
| `POSTGRES_DATABASE` | Database name (Local) | Development |
| `POSTGRES_PASSWORD` | Database password (Local) | Development |
| `POSTGRES_PORT` | Database port (Local) | Development |
| `CRON_SECRET` | Secret for cron job authentication | Both |
| `MIGRATION_SECRET` | Secret for migration endpoint | Optional |

## 🚨 Troubleshooting

### Common Issues

1. **Connection refused**: Check if PostgreSQL is running locally
2. **Migration fails**: Ensure database exists and credentials are correct
3. **Cron jobs not running**: Check Vercel cron configuration and secrets
4. **SSL errors**: Verify SSL configuration for production database

### Debug Commands

```bash
# Check database connection
curl http://localhost:3000/api/health

# Check migration status
curl http://localhost:3000/api/migrate

# View cron logs (implement in your app)
curl http://localhost:3000/api/data/cron-logs
```

## 📚 Next Steps

1. Implement actual currency data fetching in cron jobs
2. Add more sophisticated error handling and retry logic
3. Set up monitoring and alerting for cron job failures
4. Add database backup and recovery procedures
5. Implement rate limiting for API endpoints
