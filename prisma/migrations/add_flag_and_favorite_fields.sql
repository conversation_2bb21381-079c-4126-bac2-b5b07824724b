-- Migration to add flag and isFavorite fields to currencies table
-- Run this after updating the Prisma schema

-- Add the new columns
ALTER TABLE currencies 
ADD COLUMN flag VARCHAR(10),
ADD COLUMN is_favorite BOOLEAN DEFAULT false NOT NULL;

-- Create index for better query performance on favorites
CREATE INDEX idx_currencies_is_favorite ON currencies(is_favorite);

-- Set some default flags for common currencies (optional)
UPDATE currencies SET flag = '🇺🇸' WHERE symbol LIKE '%USD%';
UPDATE currencies SET flag = '🇪🇺' WHERE symbol LIKE '%EUR%';
UPDATE currencies SET flag = '🇬🇧' WHERE symbol LIKE '%GBP%';
UPDATE currencies SET flag = '🇯🇵' WHERE symbol LIKE '%JPY%';
UPDATE currencies SET flag = '🇨🇭' WHERE symbol LIKE '%CHF%';
UPDATE currencies SET flag = '🇨🇦' WHERE symbol LIKE '%CAD%';
UPDATE currencies SET flag = '🇦🇺' WHERE symbol LIKE '%AUD%';
UPDATE currencies SET flag = '₿' WHERE symbol LIKE '%BTC%';
UPDATE currencies SET flag = '⟠' WHERE symbol LIKE '%ETH%';
UPDATE currencies SET flag = '🥇' WHERE symbol LIKE '%GOLD%' OR symbol LIKE '%XAU%';
UPDATE currencies SET flag = '🥈' WHERE symbol LIKE '%SILVER%' OR symbol LIKE '%XAG%';
UPDATE currencies SET flag = '📈' WHERE symbol LIKE '%BIST%';

-- Set some popular currencies as favorites by default (optional)
UPDATE currencies SET is_favorite = true 
WHERE symbol IN ('USD/TRY', 'EUR/TRY', 'GBP/TRY', 'BTC/USD', 'GOLD', 'BIST100')
   OR symbol LIKE '%USD/TRY%' 
   OR symbol LIKE '%EUR/TRY%' 
   OR symbol LIKE '%GBP/TRY%';
