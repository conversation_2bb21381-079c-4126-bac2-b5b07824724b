// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Currency {
  id               Int       @id // Using the API's id field
  symbol           String    @db.VarChar(50) // Currency pair symbol (e.g., "USD/TRY", "BTC/USD")
  askPrice         Decimal?  @map("ask_price") @db.Decimal(20, 8) // Ask price (a field)
  bidPrice         Decimal?  @map("bid_price") @db.Decimal(20, 8) // Bid price (b field)
  lastPrice        Decimal   @map("last_price") @db.Decimal(20, 8) // Last price (lp field)
  dailyChange      Decimal?  @map("daily_change") @db.Decimal(10, 4) // Daily change percentage (d field)
  groupId          Int?      @map("group_id") // Group ID (gid field)
  precisionDigits  Int?      @map("precision_digits") // Precision digits (p field)
  openPrice        Decimal?  @map("open_price") @db.Decimal(20, 8) // Open price (o field)
  bankAsk          Decimal?  @map("bank_ask") @db.Decimal(20, 8) // Bank ask price (ba field)
  lastUpdated      DateTime? @map("last_updated") @db.Timestamptz // Last update time (l field)
  flag             String?   @db.VarChar(10) // Flag emoji for display (e.g., "🇺🇸", "🇪🇺")
  isFavorite       Boolean   @default(false) @map("is_favorite") // Whether to show in favorites/default view
  updatedAt        DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamptz
  createdAt        DateTime  @default(now()) @map("created_at") @db.Timestamptz

  // Relations
  history CurrencyHistory[]

  @@unique([id])
  @@index([symbol])
  @@index([updatedAt])
  @@index([groupId])
  @@index([isFavorite])
  @@map("currencies")
}

model CronLog {
  id           Int       @id @default(autoincrement())
  taskName     String    @map("task_name") @db.VarChar(100)
  executedAt   DateTime  @default(now()) @map("executed_at") @db.Timestamptz
  status       String    @default("pending") @db.VarChar(20)
  errorMessage String?   @map("error_message") @db.Text
  durationMs   Int?      @map("duration_ms")
  createdAt    DateTime  @default(now()) @map("created_at") @db.Timestamptz

  @@index([taskName])
  @@index([executedAt])
  @@map("cron_logs")
}

model CurrencyHistory {
  id               Int       @id @default(autoincrement())
  currencyId       Int       @map("currency_id") // References currencies.id
  symbol           String    @db.VarChar(50) // Currency pair symbol
  askPrice         Decimal?  @map("ask_price") @db.Decimal(20, 8) // Ask price (a field)
  bidPrice         Decimal?  @map("bid_price") @db.Decimal(20, 8) // Bid price (b field)
  lastPrice        Decimal   @map("last_price") @db.Decimal(20, 8) // Last price (lp field)
  dailyChange      Decimal?  @map("daily_change") @db.Decimal(10, 4) // Daily change percentage (d field)
  groupId          Int?      @map("group_id") // Group ID (gid field)
  precisionDigits  Int?      @map("precision_digits") // Precision digits (p field)
  openPrice        Decimal?  @map("open_price") @db.Decimal(20, 8) // Open price (o field)
  bankAsk          Decimal?  @map("bank_ask") @db.Decimal(20, 8) // Bank ask price (ba field)
  lastUpdated      DateTime? @map("last_updated") @db.Timestamptz // Last update time from API (l field)
  recordedAt       DateTime  @default(now()) @map("recorded_at") @db.Timestamptz // When we recorded this data
  source           String    @default("iarplatform_api") @db.VarChar(50)

  // Relations
  currency Currency @relation(fields: [currencyId], references: [id], onDelete: Cascade)

  @@index([currencyId])
  @@index([symbol])
  @@index([recordedAt])
  @@map("currency_history")
}
